'use client';

import { useState, useCallback } from 'react';

/**
 * 其他车辆出厂模态框数据接口
 */
export interface OtherVehicleExitData {
  vehicleNumber: string;
  driverName: string;
  reason: string;
}

/**
 * 其他车辆出厂模态框Hook参数
 */
export interface UseOtherVehicleExitModalProps {
  onExitConfirm?: (data: OtherVehicleExitData) => Promise<void> | void;
}

/**
 * 其他车辆出厂模态框Hook返回值
 */
export interface UseOtherVehicleExitModalReturn {
  isOpen: boolean;
  openModal: () => void;
  closeModal: () => void;
  handleConfirm: (data: OtherVehicleExitData) => Promise<void>;
}

/**
 * 其他车辆出厂模态框管理Hook
 */
export function useOtherVehicleExitModal({
  onExitConfirm,
}: UseOtherVehicleExitModalProps = {}): UseOtherVehicleExitModalReturn {
  const [isOpen, setIsOpen] = useState(false);

  const openModal = useCallback(() => {
    console.log('🚗 Opening other vehicle exit modal');
    setIsOpen(true);
  }, []);

  const closeModal = useCallback(() => {
    console.log('🚗 Closing other vehicle exit modal');
    setIsOpen(false);
  }, []);

  const handleConfirm = useCallback(
    async (data: OtherVehicleExitData) => {
      console.log('🚗 Other vehicle exit confirm:', data);
      
      try {
        if (onExitConfirm) {
          await onExitConfirm(data);
        }
        closeModal();
      } catch (error) {
        console.error('❌ Other vehicle exit confirm failed:', error);
        // 不关闭模态框，让用户重试
      }
    },
    [onExitConfirm, closeModal]
  );

  return {
    isOpen,
    openModal,
    closeModal,
    handleConfirm,
  };
}
